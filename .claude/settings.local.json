{"permissions": {"allow": ["WebFetch(domain:docs.sentry.io)", "WebFetch(domain:github.com)", "Bash(rg:*)", "Bash(grep:*)", "<PERSON><PERSON>(mix test:*)", "<PERSON><PERSON>(mix dialyzer:*)", "Bash(find:*)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg \"def get_data_category\" -A 10)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg \"def get_data_category\" -A 5 -B 5)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg \"@enforce_keys\" -A 5 lib/sentry/transaction.ex)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg \"record_discarded_events.*sample_rate\" -A 3 -B 3)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg \"record_discarded_events\" -A 2 -B 2)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/arm64-linux/rg \"dialyzer_ignore\" -B 2 -A 2)", "Bash(iex:*)", "WebFetch(domain:hexdocs.pm)", "WebFetch(domain:raw.githubusercontent.com)"], "deny": []}}