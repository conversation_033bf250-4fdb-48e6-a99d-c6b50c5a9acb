{
  "name": "sentry-elixir-latest",
  "dockerComposeFile": "../docker-compose.yml",
  "service": "dev-latest",
  // "service": "dev-1.16",
  // "service": "dev-1.14",
  // "service": "dev-1.13",
  "runServices": [
    "dev-latest"
  ],
  "workspaceFolder": "/usr/local/src/sentry-elixir",
  "features": {
    "ghcr.io/devcontainers/features/github-cli:1": {},
    "ghcr.io/nils-geistmann/devcontainers-features/zsh:0": {},
    "ghcr.io/devcontainers-extra/features/npm-packages:1": {},
    "ghcr.io/rocker-org/devcontainer-features/apt-packages:1": {
      "packages": "inotify-tools npm"
    }
  },
  "customizations": {
    "vscode": {
      "extensions": [
        "sleistner.vscode-fileutils",
        "kahole.magit",
        "JakeBecker.elixir-ls",
        "Augment.vscode-augment",
        "GitHub.vscode-pull-request-github",
        "GitHub.copilot"
      ],
      "settings": {
        "terminal.integrated.shell.linux": "/usr/local/bin/zsh",
        "editor.formatOnSave": true
      },
      "postCreateCommand": "cd ~/dotfiles && git pull --rebase && ./install.sh"
    }
  }
}
