name: Continuous Integration

on:
  push:
    branches:
      - master
      - release/**

  pull_request:

env:
  MIX_ENV: test

jobs:
  test:
    name: Test (Elixir ${{ matrix.elixir }}, OTP ${{ matrix.otp }})
    runs-on: ${{ matrix.os }}
    timeout-minutes: 10
    strategy:
      fail-fast: false
      matrix:
        # https://hexdocs.pm/elixir/compatibility-and-deprecations.html#compatibility-between-elixir-and-erlang-otp
        include:
          # Newest supported Elixir/Erlang pair.
          - elixir: '1.18.4-otp-27'
            otp: '28.0'
            lint: true
            os: ubuntu-latest

          - elixir: '1.18'
            otp: '27.2'
            lint: true
            dialyzer: true
            os: ubuntu-latest

          # One version before the last supported one.
          - elixir: '1.17'
            otp: '26.2'
            os: ubuntu-22.04

          # Oldest supported Elixir/Erlang pair that's tested;
          # However, official support goes back to OTP 22.x
          - elixir: '1.13'
            otp: '25.3'
            os: ubuntu-22.04

    steps:
    - name: Check out this repository
      uses: actions/checkout@v4

    - name: Setup Elixir and <PERSON><PERSON><PERSON>
      uses: erlef/setup-beam@v1
      with:
        elixir-version: ${{ matrix.elixir }}
        otp-version: ${{ matrix.otp }}

    - name: Cache downloaded dependencies
      uses: actions/cache@v4
      id: mix-downloaded-deps-cache
      with:
        path: |
          deps
          test_integrations/phoenix_app/deps
        key: |
          ${{ runner.os }}-${{ matrix.otp }}-${{ matrix.elixir }}-mix-deps-${{ hashFiles('**/mix.lock') }}

    - name: Download Mix dependencies
      if: steps.mix-downloaded-deps-cache.outputs.cache-hit != 'true'
      run: |
        if [ ${{ matrix.elixir }} != '1.13' ]; then
          mix deps.get --check-locked
        else
          mix deps.get
        fi

        if [ ${{ matrix.elixir }} != '1.13' ]; then
          cd test_integrations/phoenix_app

          if [ ${{ matrix.lint }} == 'true' ]; then
            mix deps.get --check-locked
          else
            mix deps.get
          fi
        fi

    # We need to manually restore and then save, so that we can save the "_build" directory
    # *without* the Elixir compiled code in it.
    - name: Cache compiled Elixir code
      uses: actions/cache@v4
      id: mix-cache
      with:
        path: |
          _build
          test_integrations/phoenix_app/_build
        key: |
          ${{ runner.os }}-${{ matrix.otp }}-${{ matrix.elixir }}-mix-${{ hashFiles('**/mix.lock') }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.otp }}-${{ matrix.elixir }}-mix-

    - name: Compile Elixir code (with --warnings-as-errors)
      if: matrix.lint
      run: |
        mix compile --warnings-as-errors
        cd test_integrations/phoenix_app
        mix compile --warnings-as-errors

    - name: Check formatting
      if: matrix.lint
      run: mix format --check-formatted

    - name: Run tests
      run: mix test

    - name: Run integration tests
      run: mix test.integrations

    - name: Cache Dialyzer PLT
      uses: actions/cache@v4
      if: matrix.dialyzer
      id: plt-cache
      with:
        path: plts
        key: |
          ${{ runner.os }}-${{ matrix.otp }}-${{ matrix.elixir }}-plts-${{ hashFiles(format('**/mix.lock')) }}
        restore-keys: |
          ${{ runner.os }}-${{ matrix.otp }}-${{ matrix.elixir }}-plts-

    - name: Create PLTs
      if: steps.plt-cache.outputs.cache-hit != 'true' && matrix.dialyzer
      run: |
        mkdir -p plts
        mix dialyzer --plt

    - name: Run dialyzer
      if: matrix.dialyzer
      run: mix dialyzer --no-check
