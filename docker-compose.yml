services:
  base: &base
    build:
      context: .
      dockerfile: Dockerfile
    working_dir: /usr/local/src/sentry-elixir
    env_file: [ .env ]
    entrypoint: .devcontainer/docker-entrypoint
    environment:
      DATABASE_HOST: postgres
    volumes:
      - .:/usr/local/src/sentry-elixir

  dev-latest: &dev-latest
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.18.4-otp-27
    env_file: [ .env ]

  dev-1.17:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.17-otp-27
    env_file: [ .env ]

  dev-1.16:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.16-otp-26
    env_file: [ .env ]

  dev-1.14:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.14-otp-26
    env_file: [ .env ]

  dev-1.13:
    <<: *base
    build:
      context: .
      dockerfile: Dockerfile
      args:
        ELIXIR_VERSION: 1.13.4-otp-24
    env_file: [ .env ]

  test:
    <<: *dev-latest
