{"name": "mcp-remote", "version": "0.1.9", "description": "Remote proxy for Model Context Protocol, allowing local-only clients to connect to remote servers using oAuth", "keywords": ["mcp", "stdio", "sse", "remote", "o<PERSON>h"], "author": "<PERSON> <<EMAIL>>", "repository": "https://github.com/geelen/mcp-remote", "type": "module", "files": ["dist", "README.md", "LICENSE"], "main": "dist/index.js", "bin": {"mcp-remote": "dist/proxy.js", "mcp-remote-client": "dist/client.js"}, "scripts": {"build": "tsup", "build:watch": "tsup --watch", "check": "prettier --check . && tsc"}, "dependencies": {"express": "^4.21.2", "open": "^10.1.0"}, "devDependencies": {"@modelcontextprotocol/sdk": "^1.11.2", "@types/express": "^5.0.0", "@types/node": "^22.13.10", "prettier": "^3.5.3", "tsup": "^8.4.0", "tsx": "^4.19.3", "typescript": "^5.8.2"}, "tsup": {"entry": ["src/client.ts", "src/proxy.ts"], "format": ["esm"], "dts": true, "clean": true, "outDir": "dist", "external": []}}