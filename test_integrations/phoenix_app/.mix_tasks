app.config:Configures all registered apps
app.start:Starts all registered apps
app.tree:Prints the application tree
archive:Lists installed archives
archive.build:Archives this project into a .ez file
archive.install:Installs an archive locally
archive.uninstall:Uninstalls archives
assets.build:Alias for tailwind phoenix_app, esbuild phoenix_app
assets.deploy:Alias for tailwind phoenix_app --minify, esbuild phoenix_app --minify, phx.digest
assets.setup:Alias for tailwind.install --if-missing, esbuild.install --if-missing
clean:Deletes generated application files
cmd:Executes the given command
compile:Compiles source files
deps:Lists dependencies and their status
deps.clean:Deletes the given dependencies' files
deps.compile:Compiles dependencies
deps.get:Gets all out of date dependencies
deps.tree:Prints the dependency tree
deps.unlock:Unlocks the given dependencies
deps.update:Updates the given dependencies
do:Executes the tasks separated by plus
ecto:Prints Ecto help information
ecto.create:Creates the repository storage
ecto.drop:Drops the repository storage
ecto.dump:Dumps the repository database structure
ecto.gen.migration:Generates a new migration for the repo
ecto.gen.repo:Generates a new repository
ecto.load:Loads previously dumped database structure
ecto.migrate:Runs the repository migrations
ecto.migrations:Displays the repository migration status
ecto.rollback:Rolls back the repository migrations
elixir_make.checksum:Fetch precompiled NIFs and build the checksums
elixir_make.precompile:Precompiles the given project for all targets
esbuild:Invokes esbuild with the profile and args
esbuild.install:Installs esbuild under _build
escript:Lists installed escripts
escript.build:Builds an escript for the project
escript.install:Installs an escript locally
escript.uninstall:Uninstalls escripts
eval:Evaluates the given code
expo.msgfmt:Generate binary message catalog from textual message description.
expo.msguniq:Unifies duplicate translations in message catalog
format:Formats the given files/patterns
gettext.extract:Extracts messages from source code
gettext.merge:Merge template files into message files
help:Prints help information for tasks
hex:Prints Hex help information
hex.audit:Shows retired Hex deps for the current project
hex.build:Builds a new package version locally
hex.config:Reads, updates or deletes local Hex config
hex.docs:Fetches or opens documentation of a package
hex.info:Prints Hex information
hex.organization:Manages Hex.pm organizations
hex.outdated:Shows outdated Hex deps for the current project
hex.owner:Manages Hex package ownership
hex.package:Fetches or diffs packages
hex.publish:Publishes a new package version
hex.registry:Manages local Hex registries
hex.repo:Manages Hex repositories
hex.retire:Retires a package version
hex.search:Searches for package names
hex.sponsor:Show Hex packages accepting sponsorships
hex.user:Manages your Hex user account
loadconfig:Loads and persists the given configuration
local:Lists tasks installed locally via archives
local.hex:Installs Hex locally
local.public_keys:Manages public keys
local.rebar:Installs Rebar locally
new:Creates a new Elixir project
oban.install:Install `igniter` in order to install Oban.
phx:Prints Phoenix help information
phx.digest:Digests and compresses static files
phx.digest.clean:Removes old versions of static assets.
phx.gen:Lists all available Phoenix generators
phx.gen.auth:Generates authentication logic for a resource
phx.gen.cert:Generates a self-signed certificate for HTTPS testing
phx.gen.channel:Generates a Phoenix channel
phx.gen.context:Generates a context with functions around an Ecto schema
phx.gen.embedded:Generates an embedded Ecto schema file
phx.gen.html:Generates context and controller for an HTML resource
phx.gen.json:Generates context and controller for a JSON resource
phx.gen.live:Generates LiveView, templates, and context for a resource
phx.gen.notifier:Generates a notifier that delivers emails by default
phx.gen.presence:Generates a Presence tracker
phx.gen.release:Generates release files and optional Dockerfile for release-based deployments
phx.gen.schema:Generates an Ecto schema and migration file
phx.gen.secret:Generates a secret
phx.gen.socket:Generates a Phoenix socket handler
phx.routes:Prints all routes
phx.server:Starts applications and their servers
profile.cprof:Profiles the given file or expression with cprof
profile.eprof:Profiles the given file or expression with eprof
profile.fprof:Profiles the given file or expression with fprof
profile.tprof:Profiles the given file or expression with tprof
release:Assembles a self-contained release
release.init:Generates sample files for releases
run:Runs the current application
sentry.package_source_code:Packages source code for Sentry to use when reporting errors
sentry.send_test_event:Send a test event to Sentry to check your Sentry configuration
swoosh.mailbox.server:Starts the mailbox preview server
tailwind:Invokes tailwind with the profile and args
tailwind.install:Installs Tailwind executable and assets
test:Runs a project's tests
test.coverage:Build report from exported test coverage
xref:Prints cross reference information
